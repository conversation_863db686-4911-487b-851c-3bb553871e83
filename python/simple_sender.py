#!/usr/bin/env python3
"""
Simple ZMQ sender for testing - minimal version
Usage: python simple_sender.py
"""

import zmq
import time
import base64
import numpy as np
import cv2
import json
import random

def create_simple_image(text="TEST"):
    """Create a simple test image"""
    img = np.zeros((1000, 3000, 3), dtype=np.uint8)
    # Simple gradient
    img[:, :, 0] = 100  # Blue
    img[:, :, 1] = 150  # Green  
    img[:, :, 2] = 200  # Red
    
    # Add text
    cv2.putText(img, f'{text} {time.strftime("%H:%M:%S")}', 
                (150, 500), cv2.FONT_HERSHEY_SIMPLEX, 5, (255, 255, 255), 8)
    return img

def send_data():
    """Send test data through single ZMQ port with different topics"""
    # Setup ZMQ connections
    context = zmq.Context()
    
    # Use single publisher for all data types
    publisher = context.socket(zmq.PUB)
    publisher.bind("tcp://*:5555")
    
    print("Starting simple data sender (PUB mode - Single Port 5555)...")
    print("Press Ctrl+C to stop")
    
    # Give subscribers time to connect
    time.sleep(1)
    
    # Base GPS coordinates
    lat, lon = 25.013722, 121.540861
    heading = 0
    
    try:
        while True:
            # Send panorama image with topic
            pano_img = create_simple_image("PANORAMA")
            _, buffer = cv2.imencode('.jpg', pano_img)
            pano_b64 = base64.b64encode(buffer.tobytes())
            publisher.send_multipart([b"panorama", pano_b64])
            
            # Send segmentation image with topic
            seg_img = create_simple_image("SEGMENTATION")
            _, buffer = cv2.imencode('.jpg', seg_img)
            seg_b64 = base64.b64encode(buffer.tobytes())
            publisher.send_multipart([b"segmentation", seg_b64])
            
            # Send GPS data with topic
            gps_data = {
                "latitude": lat,
                "longitude": lon,
                "altitude": random.randint(50, 150),
                "heading": heading,
            }
            publisher.send_multipart([b"gps", json.dumps(gps_data).encode()])
            
            print(f"Sent all data at {time.strftime('%H:%M:%S')}")
            time.sleep(0.1)  # Send every second
            
    except KeyboardInterrupt:
        print("\nStopping sender...")
    finally:
        publisher.close()
        context.term()

if __name__ == "__main__":
    send_data()
