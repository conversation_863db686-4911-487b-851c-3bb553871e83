using UnityEngine;
using UnityEngine.Events;
using UnityEngine.InputSystem;
using UnityEngine.UI;


public class ControlUI : MonoBehaviour
{
    public UnityEvent<string> OnPOIFileSelected;
    public PinManager pinManager;
    public GameObject ItemPrefab;
    public Transform ChoosePOICanva;
    public Transform ContentPanel;
    public Text StatusText;
    public Text PinCountText;
    public Transform User;
    public InputAction ReTargetUserAction;
    public float DistanceToUser = 1.0f;
    public float UIHeight = 1.2f;



    void Update()
    {
        if (pinManager != null)
        {
            PinCountText.text = $"POI數量: {pinManager.DisplayPinCount}";
        }
    }
    private void Awake()
    {
        if (ChoosePOICanva == null || ContentPanel == null || ItemPrefab == null)
        {
            Debug.LogError("ControlUI: Missing references in the inspector.");
        }
        ChoosePOICanva.gameObject.SetActive(false);
        ReTargetUserAction.Enable();
        ReTargetUserAction.performed += ctx =>
        {
            LookAtUser();
        };
        LookAtUser(); // Initial positioning
    }
    [ContextMenu("Look At User")]
    public void LookAtUser()
    {
        if (User != null)
        {
            var originalRotation = transform.rotation;
            transform.position = User.position + User.forward * DistanceToUser;
            transform.position = new Vector3(transform.position.x, UIHeight, transform.position.z);
            transform.LookAt(User.position);
            transform.rotation = Quaternion.Euler(originalRotation.eulerAngles.x, transform.rotation.eulerAngles.y + 180, originalRotation.eulerAngles.z); // Keep only Y rotation
        }
        else
        {
            Debug.LogWarning("User Transform is not assigned.");
        }
    }
    public async void ImportPOIs()
    {
        ChoosePOICanva.gameObject.SetActive(true);
        // Clear existing items
        foreach (Transform child in ContentPanel)
        {
            Destroy(child.gameObject);
        }

        // Import POIs from server
        var serverFileList = await PinImport.GetServerFileList();
        if (serverFileList == null || serverFileList.Count == 0)
        {
            Debug.LogWarning("No POI files found on the server.");
            return;
        }
        foreach (var fileName in serverFileList)
        {
            _createItem(fileName);
        }
    }
    private void _createItem(string fileName)
    {
        GameObject item = Instantiate(ItemPrefab, ContentPanel);
        item.GetComponentInChildren<Text>().text = fileName;

        Button button = item.GetComponentInChildren<Button>();
        button.onClick.AddListener(() =>
        {
            OnPOIFileSelected?.Invoke(fileName);
            ChoosePOICanva.gameObject.SetActive(false);
            StatusText.text = fileName;
        });
    }

    public void EnableAllButtons()
    {
        foreach (Button button in GetComponentsInChildren<Button>())
        {
            button.interactable = true;
        }
        foreach (Toggle toggle in GetComponentsInChildren<Toggle>())
        {
            toggle.interactable = true;
        }
    }
    public void DisableAllButtons()
    {
        foreach (Button button in GetComponentsInChildren<Button>())
        {
            button.interactable = false;
        }
        foreach (Toggle toggle in GetComponentsInChildren<Toggle>())
        {
            toggle.interactable = false;
        }
    }
}