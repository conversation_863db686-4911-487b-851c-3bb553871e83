using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;
using System.Linq;
using Mapbox.Utils;
using Mapbox.CheapRulerCs;

using Mapbox.Unity.Location;
using System.Threading.Tasks;

public class PinManager : MonoBehaviour
{
    public int DisplayPinCount = 10;
    [SerializeField] private AbstractLocationProvider _locationProvider;
    [SerializeField] private PinControl _pinPrefab;
    [SerializeField] private Transform _userTransform;
    [SerializeField] private ControlUI _controlUI;

    [SerializeField] private List<PinControl> _spawnedPins = new List<PinControl>();
    private List<PinData> _pinDatas = new List<PinData>();
    private bool _isInitialized = false;
    private List<System.Guid> _currentDisplayedPinIds = new List<System.Guid>();
    private bool _pinsVisible = true;
    
    // Height adjustment constants
    private const float DEFAULT_HEIGHT_OFFSET = 0f;
    private const float HEIGHT_ADJUSTMENT_STEP = 3f;
    private const float DIRECTION_SIMILARITY_THRESHOLD = 20.0f;

    void Start()
    {
        Assert.IsNotNull(_locationProvider, "LocationProvider is required");
        Assert.IsNotNull(_pinPrefab, "PinPrefab is required");
        Assert.IsNotNull(_userTransform, "UserTransform is required");
        Assert.IsNotNull(_controlUI, "ControlUI is required");
        _controlUI.OnPOIFileSelected.AddListener(OnImportFile);
    }

    public void AddPinCount()
    {
        DisplayPinCount += 1;
    }
    public void RemovePinCount()
    {
        if (DisplayPinCount > 0)
        {
            DisplayPinCount -= 1;
        }
    }

    public void SetPinData(List<PinData> pinDatas)
    {
        _pinDatas = pinDatas;
        _isInitialized = true;
        UpdatePins();
    }

    public async void OnImportFile(string fileName)
    {
        Debug.Log($"Importing pin data from file: {fileName}");
        var data = await PinImport.ImportFromServer(fileName);
        if (data != null && data.Count > 0)
        {
            SetPinData(data);
            Debug.Log($"Pin data imported successfully from {fileName}");
        }
        else
        {
            Debug.LogError($"Failed to import pin data from {fileName}");
        }
    }

    public void TogglePinDisplay()
    {
        SetDisplay(!_pinsVisible);
    }

    public void SetDisplay(bool visible)
    {
        _pinsVisible = visible;

        if (!visible)
        {
            foreach (var pin in _spawnedPins)
            {
                if (pin != null)
                {
                    pin.gameObject.SetActive(false);
                }
            }
        }
        else
        {
            foreach (var pin in _spawnedPins)
            {
                if (pin != null)
                {
                    pin.gameObject.SetActive(true);
                }
            }
            UpdatePins();
        }
    }

    void Update()
    {
        if (_isInitialized && _locationProvider != null && _pinsVisible)
        {
            UpdatePins();
            UpdatePinDistancesAndPositions();
            AdjustPinHeights();
        }
    }

    private void UpdatePins()
    {
        if (_pinDatas == null || _pinDatas.Count == 0 || _locationProvider == null)
            return;

        var userLocation = _locationProvider.CurrentLocation.LatitudeLongitude;
        var userHeading = _locationProvider.CurrentLocation.UserHeading;

        var ruler = new CheapRuler(userLocation.x, CheapRulerUnits.Meters);
        var closestPins = _pinDatas.Select(pinData => new
        {
            PinData = pinData,
            Distance = ruler.Distance(
                new double[] { userLocation.y, userLocation.x },
                new double[] { pinData.location.y, pinData.location.x }
            )
        })
        .OrderBy(x => x.Distance)
        .Take(DisplayPinCount)
        .Select(x => x.PinData.id)
        .ToList();

        if (ArePinListsEqual(closestPins, _currentDisplayedPinIds))
        {
            return;
        }

        var pinsToRemove = _currentDisplayedPinIds.Except(closestPins).ToList();
        
        var pinsToAdd = closestPins.Except(_currentDisplayedPinIds).ToList();

        RemovePins(pinsToRemove);

        AddPins(pinsToAdd);

        _currentDisplayedPinIds = closestPins;
    }

    private bool ArePinListsEqual(List<System.Guid> list1, List<System.Guid> list2)
    {
        if (list1.Count != list2.Count)
            return false;

        return list1.All(list2.Contains) && list2.All(list1.Contains);
    }

    private void RemovePins(List<System.Guid> pinIdsToRemove)
    {
        for (int i = _spawnedPins.Count - 1; i >= 0; i--)
        {
            var pinControl = _spawnedPins[i];
            if (pinControl != null)
            {
                if (pinIdsToRemove.Contains(pinControl.PinData.id))
                {
                    DestroyImmediate(pinControl.gameObject);
                    _spawnedPins.RemoveAt(i);
                }
            }
        }
    }

    private void AddPins(List<System.Guid> pinIdsToAdd)
    {
        if (pinIdsToAdd.Count == 0) return;
        
        var userLocation = _locationProvider.CurrentLocation.LatitudeLongitude;
        var ruler = new CheapRuler(userLocation.x, CheapRulerUnits.Meters);
        
        foreach (var pinId in pinIdsToAdd)
        {
            var pinData = _pinDatas.FirstOrDefault(p => p.id == pinId);
            if (pinData.id != System.Guid.Empty)
            {
                var pinObject = Instantiate(_pinPrefab.gameObject, transform);
                var pinControl = pinObject.GetComponent<PinControl>();
                
                if (pinControl != null)
                {
                    // Calculate distance first
                    double distance = ruler.Distance(
                        new double[] { userLocation.y, userLocation.x },
                        new double[] { pinData.location.y, pinData.location.x }
                    );
                    pinControl.CurrentDistance = distance;
                    
                    // Then initialize
                    pinControl.Init(_locationProvider, pinData, _userTransform);
                }

                pinObject.SetActive(_pinsVisible);
                _spawnedPins.Add(pinControl);
            }
        }
    }

    private void AdjustPinHeights()
    {
        if (_spawnedPins.Count <= 1 || _locationProvider == null)
            return;

        var userLocation = _locationProvider.CurrentLocation.LatitudeLongitude;
        var userHeading = _locationProvider.CurrentLocation.UserHeading;

        // Calculate direction for each pin relative to user heading (distance is already calculated and stored)
        var pinInfos = new List<PinInfo>();
        foreach (var pinControl in _spawnedPins)
        {
            if (pinControl != null && pinControl.PinData.id != System.Guid.Empty)
            {
                var direction = CalculateDirectionWithHeading(userLocation, pinControl.PinData.location, userHeading);
                // Use the already calculated distance from CurrentDistance
                var distance = pinControl.CurrentDistance;

                pinInfos.Add(new PinInfo
                {
                    PinControl = pinControl,
                    Direction = direction,
                    Distance = distance
                });
            }
        }

        // Reset all pins to default height first
        foreach (var info in pinInfos)
        {
            info.PinControl.LabelHeightOffset = DEFAULT_HEIGHT_OFFSET;
        }

        // Sort by distance (closer pins have priority for lower height)
        pinInfos.Sort((a, b) => a.Distance.CompareTo(b.Distance));

        // Adjust heights for pins with similar directions
        for (int i = 0; i < pinInfos.Count; i++)
        {
            float heightOffset = DEFAULT_HEIGHT_OFFSET;
            int heightLevel = 0;

            for (int j = 0; j < i; j++)
            {
                var angleDiff = Mathf.Abs(pinInfos[i].Direction - pinInfos[j].Direction);
                // Handle wrap-around for -180 to +180 range (e.g., -179° vs 179°)
                if (angleDiff > 180f)
                    angleDiff = 360f - angleDiff;

                // If directions are too similar, increase height level
                if (angleDiff < DIRECTION_SIMILARITY_THRESHOLD)
                {
                    heightLevel++;
                }
            }

            // Apply height offset based on level
            heightOffset += heightLevel * HEIGHT_ADJUSTMENT_STEP;
            pinInfos[i].PinControl.LabelHeightOffset = heightOffset;
        }
    }

    private float CalculateDirection(Vector2d userLocation, Vector2d pinLocation)
    {
        // Use CheapRuler for more accurate bearing calculation on Earth's surface
        var ruler = new CheapRuler(userLocation.x, CheapRulerUnits.Meters);

        // CheapRuler uses [longitude, latitude] format
        double[] userPoint = { userLocation.y, userLocation.x };
        double[] pinPoint = { pinLocation.y, pinLocation.x };

        // Calculate bearing from user to pin (returns -180 to +180)
        double bearing = ruler.Bearing(userPoint, pinPoint);

        return (float)bearing;
    }

    private float CalculateDirectionWithHeading(Vector2d userLocation, Vector2d pinLocation, float userHeading)
    {
        // Calculate absolute bearing from user to pin
        float absoluteBearing = CalculateDirection(userLocation, pinLocation);

        // Adjust bearing relative to user's heading
        // This makes pins appear in correct relative positions based on user's facing direction
        float relativeBearing = absoluteBearing - userHeading;

        // Normalize to -180 to +180 range
        while (relativeBearing > 180f)
            relativeBearing -= 360f;
        while (relativeBearing < -180f)
            relativeBearing += 360f;

        return relativeBearing;
    }

    private class PinInfo
    {
        public PinControl PinControl;
        public float Direction; // in degrees (-180 to +180)
        public double Distance; // in meters
    }

    private void ClearPins()
    {
        foreach (var pin in _spawnedPins)
        {
            if (pin != null)
            {
                DestroyImmediate(pin.gameObject);
            }
        }
        _spawnedPins.Clear();
    }

    void OnDestroy()
    {
        ClearPins();
    }

    private void UpdatePinDistancesAndPositions()
    {
        if (_spawnedPins.Count == 0 || _locationProvider == null)
            return;

        var userLocation = _locationProvider.CurrentLocation.LatitudeLongitude;
        var userHeading = _locationProvider.CurrentLocation.UserHeading;
        var ruler = new CheapRuler(userLocation.x, CheapRulerUnits.Meters);

        foreach (var pinControl in _spawnedPins)
        {
            if (pinControl != null && pinControl.PinData.id != System.Guid.Empty)
            {
                // Calculate distance and pass it directly to UpdateInfo along with user heading
                double distance = ruler.Distance(
                    new double[] { userLocation.y, userLocation.x },
                    new double[] { pinControl.PinData.location.y, pinControl.PinData.location.x }
                );

                // Update pin position and display with the calculated distance and user heading
                pinControl.UpdateInfo(distance, userHeading);
            }
        }
    }
}
